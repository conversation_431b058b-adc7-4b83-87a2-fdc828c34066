# Embedding Function 错误修复总结

## 问题描述

遇到以下错误：
```
TypeError: Expected a callable object, got <class 'NoneType'>
```

这个错误发生在 LightRAG 初始化时，`embedding_func` 参数为 `None`，但 `priority_limit_async_func_call` 函数期望一个可调用对象。

## 根本原因

1. **LightRAG 类定义**：`embedding_func` 字段的默认值是 `None`
2. **服务器初始化逻辑**：在 `lightrag_server.py` 中，embedding_func 的创建逻辑有缺陷
3. **缺少验证**：在 `__post_init__` 中没有检查 `embedding_func` 是否为 `None`

## 修复内容

### 1. 修复 lightrag_server.py 中的 embedding_func 创建逻辑

**问题**：原来的三元运算符链式调用在某些情况下会返回 `None`

**修复**：改为使用函数来创建 embedding_func，并添加默认 fallback

```python
# 修复前（有问题的代码）
embedding_func = EmbeddingFunc(
    embedding_dim=args.embedding_dim,
    max_token_size=args.max_embed_tokens,
    func=lambda texts: lollms_embed(...) if args.embedding_binding == "lollms"
         else ollama_embed(...) if args.embedding_binding == "ollama"
         else azure_openai_embed(...) if args.embedding_binding == "azure_openai"
         else openai_embed(...),  # 这里可能返回 None
)

# 修复后
def create_embedding_func():
    if args.embedding_binding == "lollms":
        return lambda texts: lollms_embed(...)
    elif args.embedding_binding == "ollama":
        return lambda texts: ollama_embed(...)
    elif args.embedding_binding == "azure_openai":
        return lambda texts: azure_openai_embed(...)
    elif args.embedding_binding == "openai":
        return lambda texts: openai_embed(...)
    else:
        # 默认 fallback 到 ollama
        logger.warning(f"Unknown embedding_binding: {args.embedding_binding}, falling back to ollama")
        return lambda texts: ollama_embed(...)

embedding_func = EmbeddingFunc(
    embedding_dim=args.embedding_dim,
    max_token_size=args.max_embed_tokens,
    func=create_embedding_func(),
)
```

### 2. 在 LightRAG.__post_init__ 中添加验证

**修复**：在使用 `embedding_func` 之前检查它是否为 `None`

```python
# 修复前
self.embedding_func = priority_limit_async_func_call(
    self.embedding_func_max_async
)(self.embedding_func)

# 修复后
if self.embedding_func is None:
    raise ValueError(
        "embedding_func is required but not provided. "
        "Please provide an EmbeddingFunc instance when creating LightRAG."
    )

self.embedding_func = priority_limit_async_func_call(
    self.embedding_func_max_async
)(self.embedding_func)
```

## 测试验证

创建了 `test_embedding_fix.py` 测试脚本来验证修复：

1. 测试不提供 embedding_func 时是否正确报错
2. 测试提供正确 embedding_func 时是否正常工作
3. 测试存储初始化是否正常
4. 测试 embedding 函数是否正常工作

## 使用建议

### 正确的 LightRAG 初始化方式

```python
from lightrag import LightRAG
from lightrag.utils import EmbeddingFunc
import numpy as np

def your_embedding_func(texts: list[str]) -> np.ndarray:
    # 你的 embedding 实现
    pass

async def your_llm_func(prompt: str, **kwargs) -> str:
    # 你的 LLM 实现
    pass

# 正确的初始化
rag = LightRAG(
    working_dir="./rag_storage",
    llm_model_func=your_llm_func,
    embedding_func=EmbeddingFunc(
        embedding_dim=384,  # 根据你的模型调整
        max_token_size=8192,
        func=your_embedding_func,
    ),
)

await rag.initialize_storages()
```

### 环境变量配置

确保设置正确的环境变量：

```bash
EMBEDDING_BINDING=ollama  # 或 openai, lollms, azure_openai
EMBEDDING_MODEL=bge-m3:latest
EMBEDDING_DIM=1024
EMBEDDING_BINDING_HOST=http://localhost:11434
EMBEDDING_BINDING_API_KEY=your_api_key
```

## 相关文件

- `lightrag/api/lightrag_server.py` - 服务器初始化逻辑
- `lightrag/lightrag.py` - LightRAG 主类
- `test_embedding_fix.py` - 测试脚本
- `embedding_func_fix_summary.md` - 本文档
