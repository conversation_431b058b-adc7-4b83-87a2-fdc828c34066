# Tiktoken 网络连接问题解决方案

## 问题描述
当遇到以下错误时：
```
HTTPSConnectionPool(host='openaipublic.blob.core.windows.net', port=443): Max retries exceeded with url: /encodings/o200k_base.tiktoken (Caused by NameResolutionError)
```

这是因为 tiktoken 需要从网络下载编码文件，但网络连接失败。

## 解决方案

### 方案 1：设置环境变量（推荐）
在运行程序前设置环境变量：

```bash
# Windows
set TIKTOKEN_CACHE_DIR=./tiktoken_cache

# Linux/Mac
export TIKTOKEN_CACHE_DIR=./tiktoken_cache
```

### 方案 2：预下载编码文件
手动下载编码文件到本地缓存目录：

```python
import tiktoken
import os

# 设置缓存目录
os.environ['TIKTOKEN_CACHE_DIR'] = './tiktoken_cache'

# 预下载常用模型的编码文件
models = ['gpt-4o-mini', 'gpt-4', 'gpt-3.5-turbo']
for model in models:
    try:
        tiktoken.encoding_for_model(model)
        print(f"Downloaded encoding for {model}")
    except Exception as e:
        print(f"Failed to download {model}: {e}")
```

### 方案 3：使用自定义 tokenizer
如果网络问题持续存在，可以使用自定义 tokenizer：

```python
from lightrag import LightRAG
from lightrag.utils import Tokenizer

class SimpleTokenizer:
    def encode(self, text: str) -> list[int]:
        # 简单的字符级分词
        return [ord(c) for c in text]
    
    def decode(self, tokens: list[int]) -> str:
        return ''.join(chr(t) for t in tokens if 0 <= t <= 1114111)

# 使用自定义 tokenizer
custom_tokenizer = Tokenizer(model_name="custom", tokenizer=SimpleTokenizer())

rag = LightRAG(
    working_dir="./rag_storage",
    tokenizer=custom_tokenizer,  # 使用自定义 tokenizer
    # 其他配置...
)
```

### 方案 4：离线模式
如果完全无法联网，可以使用已经修改过的代码，它包含了 fallback 机制。

## 代码修改说明
已经对以下文件进行了修改，增加了错误处理和 fallback 机制：

1. `lightrag/api/routers/ollama_api.py` - 缓存 tokenizer 实例
2. `lightrag/utils.py` - 增加连接错误处理
3. `lightrag/lightrag.py` - 增加 fallback tokenizer

这些修改确保即使 tiktoken 初始化失败，程序仍能正常运行。
