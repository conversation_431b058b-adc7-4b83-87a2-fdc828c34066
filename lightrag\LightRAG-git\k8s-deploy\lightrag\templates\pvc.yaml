{{- if .Values.persistence.enabled }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "lightrag.fullname" . }}-rag-storage
  labels:
    {{- include "lightrag.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.persistence.ragStorage.size }}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "lightrag.fullname" . }}-inputs
  labels:
    {{- include "lightrag.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.persistence.inputs.size }}
{{- end }}
