<!--
Thanks for contributing to LightRAG!

Please ensure your pull request is ready for review before submitting.

About this template

This template helps contributors provide a clear and concise description of their changes. Feel free to adjust it as needed.
-->

## Description

[Briefly describe the changes made in this pull request.]

## Related Issues

[Reference any related issues or tasks addressed by this pull request.]

## Changes Made

[List the specific changes made in this pull request.]

## Checklist

- [ ] Changes tested locally
- [ ] Code reviewed
- [ ] Documentation updated (if necessary)
- [ ] Unit tests added (if applicable)

## Additional Notes

[Add any additional notes or context for the reviewer(s).]
