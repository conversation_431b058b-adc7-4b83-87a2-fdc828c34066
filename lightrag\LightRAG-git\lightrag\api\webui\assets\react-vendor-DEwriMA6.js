function rt(e,t){for(var r=0;r<t.length;r++){const o=t[r];if(typeof o!="string"&&!Array.isArray(o)){for(const a in o)if(a!=="default"&&!(a in e)){const u=Object.getOwnPropertyDescriptor(o,a);u&&Object.defineProperty(e,a,u.get?u:{enumerable:!0,get:()=>o[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var Lr=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ae(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function kr(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function o(){return this instanceof o?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(o){var a=Object.getOwnPropertyDescriptor(e,o);Object.defineProperty(r,o,a.get?a:{enumerable:!0,get:function(){return e[o]}})}),r}var ce={exports:{}},_={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xe;function nt(){if(xe)return _;xe=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),u=Symbol.for("react.consumer"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),n=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),g=Symbol.iterator;function y(i){return i===null||typeof i!="object"?null:(i=g&&i[g]||i["@@iterator"],typeof i=="function"?i:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},p=Object.assign,w={};function v(i,h,x){this.props=i,this.context=h,this.refs=w,this.updater=x||E}v.prototype.isReactComponent={},v.prototype.setState=function(i,h){if(typeof i!="object"&&typeof i!="function"&&i!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,i,h,"setState")},v.prototype.forceUpdate=function(i){this.updater.enqueueForceUpdate(this,i,"forceUpdate")};function m(){}m.prototype=v.prototype;function C(i,h,x){this.props=i,this.context=h,this.refs=w,this.updater=x||E}var R=C.prototype=new m;R.constructor=C,p(R,v.prototype),R.isPureReactComponent=!0;var O=Array.isArray,b={H:null,A:null,T:null,S:null},U=Object.prototype.hasOwnProperty;function N(i,h,x,S,T,k){return x=k.ref,{$$typeof:e,type:i,key:h,ref:x!==void 0?x:null,props:k}}function F(i,h){return N(i.type,h,void 0,void 0,void 0,i.props)}function A(i){return typeof i=="object"&&i!==null&&i.$$typeof===e}function M(i){var h={"=":"=0",":":"=2"};return"$"+i.replace(/[=:]/g,function(x){return h[x]})}var G=/\/+/g;function se(i,h){return typeof i=="object"&&i!==null&&i.key!=null?M(""+i.key):h.toString(36)}function we(){}function Qe(i){switch(i.status){case"fulfilled":return i.value;case"rejected":throw i.reason;default:switch(typeof i.status=="string"?i.then(we,we):(i.status="pending",i.then(function(h){i.status==="pending"&&(i.status="fulfilled",i.value=h)},function(h){i.status==="pending"&&(i.status="rejected",i.reason=h)})),i.status){case"fulfilled":return i.value;case"rejected":throw i.reason}}throw i}function K(i,h,x,S,T){var k=typeof i;(k==="undefined"||k==="boolean")&&(i=null);var P=!1;if(i===null)P=!0;else switch(k){case"bigint":case"string":case"number":P=!0;break;case"object":switch(i.$$typeof){case e:case t:P=!0;break;case d:return P=i._init,K(P(i._payload),h,x,S,T)}}if(P)return T=T(i),P=S===""?"."+se(i,0):S,O(T)?(x="",P!=null&&(x=P.replace(G,"$&/")+"/"),K(T,h,x,"",function(tt){return tt})):T!=null&&(A(T)&&(T=F(T,x+(T.key==null||i&&i.key===T.key?"":(""+T.key).replace(G,"$&/")+"/")+P)),h.push(T)),1;P=0;var z=S===""?".":S+":";if(O(i))for(var $=0;$<i.length;$++)S=i[$],k=z+se(S,$),P+=K(S,h,x,k,T);else if($=y(i),typeof $=="function")for(i=$.call(i),$=0;!(S=i.next()).done;)S=S.value,k=z+se(S,$++),P+=K(S,h,x,k,T);else if(k==="object"){if(typeof i.then=="function")return K(Qe(i),h,x,S,T);throw h=String(i),Error("Objects are not valid as a React child (found: "+(h==="[object Object]"?"object with keys {"+Object.keys(i).join(", ")+"}":h)+"). If you meant to render a collection of children, use an array instead.")}return P}function re(i,h,x){if(i==null)return i;var S=[],T=0;return K(i,S,"","",function(k){return h.call(x,k,T++)}),S}function Ze(i){if(i._status===-1){var h=i._result;h=h(),h.then(function(x){(i._status===0||i._status===-1)&&(i._status=1,i._result=x)},function(x){(i._status===0||i._status===-1)&&(i._status=2,i._result=x)}),i._status===-1&&(i._status=0,i._result=h)}if(i._status===1)return i._result.default;throw i._result}var Ce=typeof reportError=="function"?reportError:function(i){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var h=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof i=="object"&&i!==null&&typeof i.message=="string"?String(i.message):String(i),error:i});if(!window.dispatchEvent(h))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",i);return}console.error(i)};function et(){}return _.Children={map:re,forEach:function(i,h,x){re(i,function(){h.apply(this,arguments)},x)},count:function(i){var h=0;return re(i,function(){h++}),h},toArray:function(i){return re(i,function(h){return h})||[]},only:function(i){if(!A(i))throw Error("React.Children.only expected to receive a single React element child.");return i}},_.Component=v,_.Fragment=r,_.Profiler=a,_.PureComponent=C,_.StrictMode=o,_.Suspense=l,_.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=b,_.act=function(){throw Error("act(...) is not supported in production builds of React.")},_.cache=function(i){return function(){return i.apply(null,arguments)}},_.cloneElement=function(i,h,x){if(i==null)throw Error("The argument must be a React element, but you passed "+i+".");var S=p({},i.props),T=i.key,k=void 0;if(h!=null)for(P in h.ref!==void 0&&(k=void 0),h.key!==void 0&&(T=""+h.key),h)!U.call(h,P)||P==="key"||P==="__self"||P==="__source"||P==="ref"&&h.ref===void 0||(S[P]=h[P]);var P=arguments.length-2;if(P===1)S.children=x;else if(1<P){for(var z=Array(P),$=0;$<P;$++)z[$]=arguments[$+2];S.children=z}return N(i.type,T,void 0,void 0,k,S)},_.createContext=function(i){return i={$$typeof:s,_currentValue:i,_currentValue2:i,_threadCount:0,Provider:null,Consumer:null},i.Provider=i,i.Consumer={$$typeof:u,_context:i},i},_.createElement=function(i,h,x){var S,T={},k=null;if(h!=null)for(S in h.key!==void 0&&(k=""+h.key),h)U.call(h,S)&&S!=="key"&&S!=="__self"&&S!=="__source"&&(T[S]=h[S]);var P=arguments.length-2;if(P===1)T.children=x;else if(1<P){for(var z=Array(P),$=0;$<P;$++)z[$]=arguments[$+2];T.children=z}if(i&&i.defaultProps)for(S in P=i.defaultProps,P)T[S]===void 0&&(T[S]=P[S]);return N(i,k,void 0,void 0,null,T)},_.createRef=function(){return{current:null}},_.forwardRef=function(i){return{$$typeof:c,render:i}},_.isValidElement=A,_.lazy=function(i){return{$$typeof:d,_payload:{_status:-1,_result:i},_init:Ze}},_.memo=function(i,h){return{$$typeof:n,type:i,compare:h===void 0?null:h}},_.startTransition=function(i){var h=b.T,x={};b.T=x;try{var S=i(),T=b.S;T!==null&&T(x,S),typeof S=="object"&&S!==null&&typeof S.then=="function"&&S.then(et,Ce)}catch(k){Ce(k)}finally{b.T=h}},_.unstable_useCacheRefresh=function(){return b.H.useCacheRefresh()},_.use=function(i){return b.H.use(i)},_.useActionState=function(i,h,x){return b.H.useActionState(i,h,x)},_.useCallback=function(i,h){return b.H.useCallback(i,h)},_.useContext=function(i){return b.H.useContext(i)},_.useDebugValue=function(){},_.useDeferredValue=function(i,h){return b.H.useDeferredValue(i,h)},_.useEffect=function(i,h){return b.H.useEffect(i,h)},_.useId=function(){return b.H.useId()},_.useImperativeHandle=function(i,h,x){return b.H.useImperativeHandle(i,h,x)},_.useInsertionEffect=function(i,h){return b.H.useInsertionEffect(i,h)},_.useLayoutEffect=function(i,h){return b.H.useLayoutEffect(i,h)},_.useMemo=function(i,h){return b.H.useMemo(i,h)},_.useOptimistic=function(i,h){return b.H.useOptimistic(i,h)},_.useReducer=function(i,h,x){return b.H.useReducer(i,h,x)},_.useRef=function(i){return b.H.useRef(i)},_.useState=function(i){return b.H.useState(i)},_.useSyncExternalStore=function(i,h,x){return b.H.useSyncExternalStore(i,h,x)},_.useTransition=function(){return b.H.useTransition()},_.version="19.0.0",_}var Se;function De(){return Se||(Se=1,ce.exports=nt()),ce.exports}var f=De();const at=Ae(f),$r=rt({__proto__:null,default:at},[f]);var fe={exports:{}},D={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _e;function ot(){if(_e)return D;_e=1;var e=De();function t(l){var n="https://react.dev/errors/"+l;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var d=2;d<arguments.length;d++)n+="&args[]="+encodeURIComponent(arguments[d])}return"Minified React error #"+l+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var o={d:{f:r,r:function(){throw Error(t(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},a=Symbol.for("react.portal");function u(l,n,d){var g=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:a,key:g==null?null:""+g,children:l,containerInfo:n,implementation:d}}var s=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(l,n){if(l==="font")return"";if(typeof n=="string")return n==="use-credentials"?n:""}return D.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=o,D.createPortal=function(l,n){var d=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!n||n.nodeType!==1&&n.nodeType!==9&&n.nodeType!==11)throw Error(t(299));return u(l,n,null,d)},D.flushSync=function(l){var n=s.T,d=o.p;try{if(s.T=null,o.p=2,l)return l()}finally{s.T=n,o.p=d,o.d.f()}},D.preconnect=function(l,n){typeof l=="string"&&(n?(n=n.crossOrigin,n=typeof n=="string"?n==="use-credentials"?n:"":void 0):n=null,o.d.C(l,n))},D.prefetchDNS=function(l){typeof l=="string"&&o.d.D(l)},D.preinit=function(l,n){if(typeof l=="string"&&n&&typeof n.as=="string"){var d=n.as,g=c(d,n.crossOrigin),y=typeof n.integrity=="string"?n.integrity:void 0,E=typeof n.fetchPriority=="string"?n.fetchPriority:void 0;d==="style"?o.d.S(l,typeof n.precedence=="string"?n.precedence:void 0,{crossOrigin:g,integrity:y,fetchPriority:E}):d==="script"&&o.d.X(l,{crossOrigin:g,integrity:y,fetchPriority:E,nonce:typeof n.nonce=="string"?n.nonce:void 0})}},D.preinitModule=function(l,n){if(typeof l=="string")if(typeof n=="object"&&n!==null){if(n.as==null||n.as==="script"){var d=c(n.as,n.crossOrigin);o.d.M(l,{crossOrigin:d,integrity:typeof n.integrity=="string"?n.integrity:void 0,nonce:typeof n.nonce=="string"?n.nonce:void 0})}}else n==null&&o.d.M(l)},D.preload=function(l,n){if(typeof l=="string"&&typeof n=="object"&&n!==null&&typeof n.as=="string"){var d=n.as,g=c(d,n.crossOrigin);o.d.L(l,d,{crossOrigin:g,integrity:typeof n.integrity=="string"?n.integrity:void 0,nonce:typeof n.nonce=="string"?n.nonce:void 0,type:typeof n.type=="string"?n.type:void 0,fetchPriority:typeof n.fetchPriority=="string"?n.fetchPriority:void 0,referrerPolicy:typeof n.referrerPolicy=="string"?n.referrerPolicy:void 0,imageSrcSet:typeof n.imageSrcSet=="string"?n.imageSrcSet:void 0,imageSizes:typeof n.imageSizes=="string"?n.imageSizes:void 0,media:typeof n.media=="string"?n.media:void 0})}},D.preloadModule=function(l,n){if(typeof l=="string")if(n){var d=c(n.as,n.crossOrigin);o.d.m(l,{as:typeof n.as=="string"&&n.as!=="script"?n.as:void 0,crossOrigin:d,integrity:typeof n.integrity=="string"?n.integrity:void 0})}else o.d.m(l)},D.requestFormReset=function(l){o.d.r(l)},D.unstable_batchedUpdates=function(l,n){return l(n)},D.useFormState=function(l,n,d){return s.H.useFormState(l,n,d)},D.useFormStatus=function(){return s.H.useHostTransitionStatus()},D.version="19.0.0",D}var be;function it(){if(be)return fe.exports;be=1;function e(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}return e(),fe.exports=ot(),fe.exports}var J={},Pe;function ut(){if(Pe)return J;Pe=1,Object.defineProperty(J,"__esModule",{value:!0}),J.parse=s,J.serialize=n;const e=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,t=/^[\u0021-\u003A\u003C-\u007E]*$/,r=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,u=(()=>{const y=function(){};return y.prototype=Object.create(null),y})();function s(y,E){const p=new u,w=y.length;if(w<2)return p;const v=(E==null?void 0:E.decode)||d;let m=0;do{const C=y.indexOf("=",m);if(C===-1)break;const R=y.indexOf(";",m),O=R===-1?w:R;if(C>O){m=y.lastIndexOf(";",C-1)+1;continue}const b=c(y,m,C),U=l(y,C,b),N=y.slice(b,U);if(p[N]===void 0){let F=c(y,C+1,O),A=l(y,O,F);const M=v(y.slice(F,A));p[N]=M}m=O+1}while(m<w);return p}function c(y,E,p){do{const w=y.charCodeAt(E);if(w!==32&&w!==9)return E}while(++E<p);return p}function l(y,E,p){for(;E>p;){const w=y.charCodeAt(--E);if(w!==32&&w!==9)return E+1}return p}function n(y,E,p){const w=(p==null?void 0:p.encode)||encodeURIComponent;if(!e.test(y))throw new TypeError(`argument name is invalid: ${y}`);const v=w(E);if(!t.test(v))throw new TypeError(`argument val is invalid: ${E}`);let m=y+"="+v;if(!p)return m;if(p.maxAge!==void 0){if(!Number.isInteger(p.maxAge))throw new TypeError(`option maxAge is invalid: ${p.maxAge}`);m+="; Max-Age="+p.maxAge}if(p.domain){if(!r.test(p.domain))throw new TypeError(`option domain is invalid: ${p.domain}`);m+="; Domain="+p.domain}if(p.path){if(!o.test(p.path))throw new TypeError(`option path is invalid: ${p.path}`);m+="; Path="+p.path}if(p.expires){if(!g(p.expires)||!Number.isFinite(p.expires.valueOf()))throw new TypeError(`option expires is invalid: ${p.expires}`);m+="; Expires="+p.expires.toUTCString()}if(p.httpOnly&&(m+="; HttpOnly"),p.secure&&(m+="; Secure"),p.partitioned&&(m+="; Partitioned"),p.priority)switch(typeof p.priority=="string"?p.priority.toLowerCase():void 0){case"low":m+="; Priority=Low";break;case"medium":m+="; Priority=Medium";break;case"high":m+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${p.priority}`)}if(p.sameSite)switch(typeof p.sameSite=="string"?p.sameSite.toLowerCase():p.sameSite){case!0:case"strict":m+="; SameSite=Strict";break;case"lax":m+="; SameSite=Lax";break;case"none":m+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${p.sameSite}`)}return m}function d(y){if(y.indexOf("%")===-1)return y;try{return decodeURIComponent(y)}catch{return y}}function g(y){return a.call(y)==="[object Date]"}return J}ut();/**
 * react-router v7.3.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Te="popstate";function lt(e={}){function t(a,u){let{pathname:s="/",search:c="",hash:l=""}=Y(a.location.hash.substring(1));return!s.startsWith("/")&&!s.startsWith(".")&&(s="/"+s),pe("",{pathname:s,search:c,hash:l},u.state&&u.state.usr||null,u.state&&u.state.key||"default")}function r(a,u){let s=a.document.querySelector("base"),c="";if(s&&s.getAttribute("href")){let l=a.location.href,n=l.indexOf("#");c=n===-1?l:l.slice(0,n)}return c+"#"+(typeof u=="string"?u:Q(u))}function o(a,u){I(a.pathname.charAt(0)==="/",`relative pathnames are not supported in hash history.push(${JSON.stringify(u)})`)}return ct(t,r,o,e)}function L(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function I(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function st(){return Math.random().toString(36).substring(2,10)}function Oe(e,t){return{usr:e.state,key:e.key,idx:t}}function pe(e,t,r=null,o){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?Y(t):t,state:r,key:t&&t.key||o||st()}}function Q({pathname:e="/",search:t="",hash:r=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),r&&r!=="#"&&(e+=r.charAt(0)==="#"?r:"#"+r),e}function Y(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substring(r),e=e.substring(0,r));let o=e.indexOf("?");o>=0&&(t.search=e.substring(o),e=e.substring(0,o)),e&&(t.pathname=e)}return t}function ct(e,t,r,o={}){let{window:a=document.defaultView,v5Compat:u=!1}=o,s=a.history,c="POP",l=null,n=d();n==null&&(n=0,s.replaceState({...s.state,idx:n},""));function d(){return(s.state||{idx:null}).idx}function g(){c="POP";let v=d(),m=v==null?null:v-n;n=v,l&&l({action:c,location:w.location,delta:m})}function y(v,m){c="PUSH";let C=pe(w.location,v,m);r&&r(C,v),n=d()+1;let R=Oe(C,n),O=w.createHref(C);try{s.pushState(R,"",O)}catch(b){if(b instanceof DOMException&&b.name==="DataCloneError")throw b;a.location.assign(O)}u&&l&&l({action:c,location:w.location,delta:1})}function E(v,m){c="REPLACE";let C=pe(w.location,v,m);r&&r(C,v),n=d();let R=Oe(C,n),O=w.createHref(C);s.replaceState(R,"",O),u&&l&&l({action:c,location:w.location,delta:0})}function p(v){let m=a.location.origin!=="null"?a.location.origin:a.location.href,C=typeof v=="string"?v:Q(v);return C=C.replace(/ $/,"%20"),L(m,`No window.location.(origin|href) available to create URL for href: ${C}`),new URL(C,m)}let w={get action(){return c},get location(){return e(a,s)},listen(v){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(Te,g),l=v,()=>{a.removeEventListener(Te,g),l=null}},createHref(v){return t(a,v)},createURL:p,encodeLocation(v){let m=p(v);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:y,replace:E,go(v){return s.go(v)}};return w}function Ne(e,t,r="/"){return ft(e,t,r,!1)}function ft(e,t,r,o){let a=typeof t=="string"?Y(t):t,u=B(a.pathname||"/",r);if(u==null)return null;let s=Ie(e);dt(s);let c=null;for(let l=0;c==null&&l<s.length;++l){let n=xt(u);c=wt(s[l],n,o)}return c}function Ie(e,t=[],r=[],o=""){let a=(u,s,c)=>{let l={relativePath:c===void 0?u.path||"":c,caseSensitive:u.caseSensitive===!0,childrenIndex:s,route:u};l.relativePath.startsWith("/")&&(L(l.relativePath.startsWith(o),`Absolute route path "${l.relativePath}" nested under path "${o}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),l.relativePath=l.relativePath.slice(o.length));let n=j([o,l.relativePath]),d=r.concat(l);u.children&&u.children.length>0&&(L(u.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${n}".`),Ie(u.children,t,d,n)),!(u.path==null&&!u.index)&&t.push({path:n,score:Et(n,u.index),routesMeta:d})};return e.forEach((u,s)=>{var c;if(u.path===""||!((c=u.path)!=null&&c.includes("?")))a(u,s);else for(let l of Me(u.path))a(u,s,l)}),t}function Me(e){let t=e.split("/");if(t.length===0)return[];let[r,...o]=t,a=r.endsWith("?"),u=r.replace(/\?$/,"");if(o.length===0)return a?[u,""]:[u];let s=Me(o.join("/")),c=[];return c.push(...s.map(l=>l===""?u:[u,l].join("/"))),a&&c.push(...s),c.map(l=>e.startsWith("/")&&l===""?"/":l)}function dt(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Rt(t.routesMeta.map(o=>o.childrenIndex),r.routesMeta.map(o=>o.childrenIndex)))}var ht=/^:[\w-]+$/,pt=3,mt=2,yt=1,gt=10,vt=-2,Le=e=>e==="*";function Et(e,t){let r=e.split("/"),o=r.length;return r.some(Le)&&(o+=vt),t&&(o+=mt),r.filter(a=>!Le(a)).reduce((a,u)=>a+(ht.test(u)?pt:u===""?yt:gt),o)}function Rt(e,t){return e.length===t.length&&e.slice(0,-1).every((o,a)=>o===t[a])?e[e.length-1]-t[t.length-1]:0}function wt(e,t,r=!1){let{routesMeta:o}=e,a={},u="/",s=[];for(let c=0;c<o.length;++c){let l=o[c],n=c===o.length-1,d=u==="/"?t:t.slice(u.length)||"/",g=ie({path:l.relativePath,caseSensitive:l.caseSensitive,end:n},d),y=l.route;if(!g&&n&&r&&!o[o.length-1].route.index&&(g=ie({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},d)),!g)return null;Object.assign(a,g.params),s.push({params:a,pathname:j([u,g.pathname]),pathnameBase:Pt(j([u,g.pathnameBase])),route:y}),g.pathnameBase!=="/"&&(u=j([u,g.pathnameBase]))}return s}function ie(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,o]=Ct(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let u=a[0],s=u.replace(/(.)\/+$/,"$1"),c=a.slice(1);return{params:o.reduce((n,{paramName:d,isOptional:g},y)=>{if(d==="*"){let p=c[y]||"";s=u.slice(0,u.length-p.length).replace(/(.)\/+$/,"$1")}const E=c[y];return g&&!E?n[d]=void 0:n[d]=(E||"").replace(/%2F/g,"/"),n},{}),pathname:u,pathnameBase:s,pattern:e}}function Ct(e,t=!1,r=!0){I(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let o=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,c,l)=>(o.push({paramName:c,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(o.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),o]}function xt(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return I(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function B(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,o=e.charAt(r);return o&&o!=="/"?null:e.slice(r)||"/"}function St(e,t="/"){let{pathname:r,search:o="",hash:a=""}=typeof e=="string"?Y(e):e;return{pathname:r?r.startsWith("/")?r:_t(r,t):t,search:Tt(o),hash:Ot(a)}}function _t(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function de(e,t,r,o){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(o)}].  Please separate it out to the \`to.${r}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function bt(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function He(e){let t=bt(e);return t.map((r,o)=>o===t.length-1?r.pathname:r.pathnameBase)}function Ue(e,t,r,o=!1){let a;typeof e=="string"?a=Y(e):(a={...e},L(!a.pathname||!a.pathname.includes("?"),de("?","pathname","search",a)),L(!a.pathname||!a.pathname.includes("#"),de("#","pathname","hash",a)),L(!a.search||!a.search.includes("#"),de("#","search","hash",a)));let u=e===""||a.pathname==="",s=u?"/":a.pathname,c;if(s==null)c=r;else{let g=t.length-1;if(!o&&s.startsWith("..")){let y=s.split("/");for(;y[0]==="..";)y.shift(),g-=1;a.pathname=y.join("/")}c=g>=0?t[g]:"/"}let l=St(a,c),n=s&&s!=="/"&&s.endsWith("/"),d=(u||s===".")&&r.endsWith("/");return!l.pathname.endsWith("/")&&(n||d)&&(l.pathname+="/"),l}var j=e=>e.join("/").replace(/\/\/+/g,"/"),Pt=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Tt=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ot=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Lt(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var Fe=["POST","PUT","PATCH","DELETE"];new Set(Fe);var kt=["GET",...Fe];new Set(kt);var V=f.createContext(null);V.displayName="DataRouter";var ue=f.createContext(null);ue.displayName="DataRouterState";var je=f.createContext({isTransitioning:!1});je.displayName="ViewTransition";var $t=f.createContext(new Map);$t.displayName="Fetchers";var At=f.createContext(null);At.displayName="Await";var H=f.createContext(null);H.displayName="Navigation";var Z=f.createContext(null);Z.displayName="Location";var W=f.createContext({outlet:null,matches:[],isDataRoute:!1});W.displayName="Route";var ye=f.createContext(null);ye.displayName="RouteError";function Dt(e,{relative:t}={}){L(ee(),"useHref() may be used only in the context of a <Router> component.");let{basename:r,navigator:o}=f.useContext(H),{hash:a,pathname:u,search:s}=te(e,{relative:t}),c=u;return r!=="/"&&(c=u==="/"?r:j([r,u])),o.createHref({pathname:c,search:s,hash:a})}function ee(){return f.useContext(Z)!=null}function q(){return L(ee(),"useLocation() may be used only in the context of a <Router> component."),f.useContext(Z).location}var Be="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function We(e){f.useContext(H).static||f.useLayoutEffect(e)}function Nt(){let{isDataRoute:e}=f.useContext(W);return e?Vt():It()}function It(){L(ee(),"useNavigate() may be used only in the context of a <Router> component.");let e=f.useContext(V),{basename:t,navigator:r}=f.useContext(H),{matches:o}=f.useContext(W),{pathname:a}=q(),u=JSON.stringify(He(o)),s=f.useRef(!1);return We(()=>{s.current=!0}),f.useCallback((l,n={})=>{if(I(s.current,Be),!s.current)return;if(typeof l=="number"){r.go(l);return}let d=Ue(l,JSON.parse(u),a,n.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:j([t,d.pathname])),(n.replace?r.replace:r.push)(d,n.state,n)},[t,r,u,a,e])}f.createContext(null);function te(e,{relative:t}={}){let{matches:r}=f.useContext(W),{pathname:o}=q(),a=JSON.stringify(He(r));return f.useMemo(()=>Ue(e,JSON.parse(a),o,t==="path"),[e,a,o,t])}function Mt(e,t){return ze(e,t)}function ze(e,t,r,o){var C;L(ee(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a,static:u}=f.useContext(H),{matches:s}=f.useContext(W),c=s[s.length-1],l=c?c.params:{},n=c?c.pathname:"/",d=c?c.pathnameBase:"/",g=c&&c.route;{let R=g&&g.path||"";Ye(n,!g||R.endsWith("*")||R.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${n}" (under <Route path="${R}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${R}"> to <Route path="${R==="/"?"*":`${R}/*`}">.`)}let y=q(),E;if(t){let R=typeof t=="string"?Y(t):t;L(d==="/"||((C=R.pathname)==null?void 0:C.startsWith(d)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${d}" but pathname "${R.pathname}" was given in the \`location\` prop.`),E=R}else E=y;let p=E.pathname||"/",w=p;if(d!=="/"){let R=d.replace(/^\//,"").split("/");w="/"+p.replace(/^\//,"").split("/").slice(R.length).join("/")}let v=!u&&r&&r.matches&&r.matches.length>0?r.matches:Ne(e,{pathname:w});I(g||v!=null,`No routes matched location "${E.pathname}${E.search}${E.hash}" `),I(v==null||v[v.length-1].route.element!==void 0||v[v.length-1].route.Component!==void 0||v[v.length-1].route.lazy!==void 0,`Matched leaf route at location "${E.pathname}${E.search}${E.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let m=Bt(v&&v.map(R=>Object.assign({},R,{params:Object.assign({},l,R.params),pathname:j([d,a.encodeLocation?a.encodeLocation(R.pathname).pathname:R.pathname]),pathnameBase:R.pathnameBase==="/"?d:j([d,a.encodeLocation?a.encodeLocation(R.pathnameBase).pathname:R.pathnameBase])})),s,r,o);return t&&m?f.createElement(Z.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...E},navigationType:"POP"}},m):m}function Ht(){let e=Kt(),t=Lt(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,o="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:o},u={padding:"2px 4px",backgroundColor:o},s=null;return console.error("Error handled by React Router default ErrorBoundary:",e),s=f.createElement(f.Fragment,null,f.createElement("p",null,"💿 Hey developer 👋"),f.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",f.createElement("code",{style:u},"ErrorBoundary")," or"," ",f.createElement("code",{style:u},"errorElement")," prop on your route.")),f.createElement(f.Fragment,null,f.createElement("h2",null,"Unexpected Application Error!"),f.createElement("h3",{style:{fontStyle:"italic"}},t),r?f.createElement("pre",{style:a},r):null,s)}var Ut=f.createElement(Ht,null),Ft=class extends f.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?f.createElement(W.Provider,{value:this.props.routeContext},f.createElement(ye.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function jt({routeContext:e,match:t,children:r}){let o=f.useContext(V);return o&&o.static&&o.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=t.route.id),f.createElement(W.Provider,{value:e},r)}function Bt(e,t=[],r=null,o=null){if(e==null){if(!r)return null;if(r.errors)e=r.matches;else if(t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let a=e,u=r==null?void 0:r.errors;if(u!=null){let l=a.findIndex(n=>n.route.id&&(u==null?void 0:u[n.route.id])!==void 0);L(l>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(u).join(",")}`),a=a.slice(0,Math.min(a.length,l+1))}let s=!1,c=-1;if(r)for(let l=0;l<a.length;l++){let n=a[l];if((n.route.HydrateFallback||n.route.hydrateFallbackElement)&&(c=l),n.route.id){let{loaderData:d,errors:g}=r,y=n.route.loader&&!d.hasOwnProperty(n.route.id)&&(!g||g[n.route.id]===void 0);if(n.route.lazy||y){s=!0,c>=0?a=a.slice(0,c+1):a=[a[0]];break}}}return a.reduceRight((l,n,d)=>{let g,y=!1,E=null,p=null;r&&(g=u&&n.route.id?u[n.route.id]:void 0,E=n.route.errorElement||Ut,s&&(c<0&&d===0?(Ye("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),y=!0,p=null):c===d&&(y=!0,p=n.route.hydrateFallbackElement||null)));let w=t.concat(a.slice(0,d+1)),v=()=>{let m;return g?m=E:y?m=p:n.route.Component?m=f.createElement(n.route.Component,null):n.route.element?m=n.route.element:m=l,f.createElement(jt,{match:n,routeContext:{outlet:l,matches:w,isDataRoute:r!=null},children:m})};return r&&(n.route.ErrorBoundary||n.route.errorElement||d===0)?f.createElement(Ft,{location:r.location,revalidation:r.revalidation,component:E,error:g,children:v(),routeContext:{outlet:null,matches:w,isDataRoute:!0}}):v()},null)}function ge(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Wt(e){let t=f.useContext(V);return L(t,ge(e)),t}function zt(e){let t=f.useContext(ue);return L(t,ge(e)),t}function Yt(e){let t=f.useContext(W);return L(t,ge(e)),t}function ve(e){let t=Yt(e),r=t.matches[t.matches.length-1];return L(r.route.id,`${e} can only be used on routes that contain a unique "id"`),r.route.id}function qt(){return ve("useRouteId")}function Kt(){var o;let e=f.useContext(ye),t=zt("useRouteError"),r=ve("useRouteError");return e!==void 0?e:(o=t.errors)==null?void 0:o[r]}function Vt(){let{router:e}=Wt("useNavigate"),t=ve("useNavigate"),r=f.useRef(!1);return We(()=>{r.current=!0}),f.useCallback(async(a,u={})=>{I(r.current,Be),r.current&&(typeof a=="number"?e.navigate(a):await e.navigate(a,{fromRouteId:t,...u}))},[e,t])}var ke={};function Ye(e,t,r){!t&&!ke[e]&&(ke[e]=!0,I(!1,r))}f.memo(Gt);function Gt({routes:e,future:t,state:r}){return ze(e,void 0,r,t)}function Jt(e){L(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Xt({basename:e="/",children:t=null,location:r,navigationType:o="POP",navigator:a,static:u=!1}){L(!ee(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=e.replace(/^\/*/,"/"),c=f.useMemo(()=>({basename:s,navigator:a,static:u,future:{}}),[s,a,u]);typeof r=="string"&&(r=Y(r));let{pathname:l="/",search:n="",hash:d="",state:g=null,key:y="default"}=r,E=f.useMemo(()=>{let p=B(l,s);return p==null?null:{location:{pathname:p,search:n,hash:d,state:g,key:y},navigationType:o}},[s,l,n,d,g,y,o]);return I(E!=null,`<Router basename="${s}"> is not able to match the URL "${l}${n}${d}" because it does not start with the basename, so the <Router> won't render anything.`),E==null?null:f.createElement(H.Provider,{value:c},f.createElement(Z.Provider,{children:t,value:E}))}function Ar({children:e,location:t}){return Mt(me(e),t)}function me(e,t=[]){let r=[];return f.Children.forEach(e,(o,a)=>{if(!f.isValidElement(o))return;let u=[...t,a];if(o.type===f.Fragment){r.push.apply(r,me(o.props.children,u));return}L(o.type===Jt,`[${typeof o.type=="string"?o.type:o.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),L(!o.props.index||!o.props.children,"An index route cannot have child routes.");let s={id:o.props.id||u.join("-"),caseSensitive:o.props.caseSensitive,element:o.props.element,Component:o.props.Component,index:o.props.index,path:o.props.path,loader:o.props.loader,action:o.props.action,hydrateFallbackElement:o.props.hydrateFallbackElement,HydrateFallback:o.props.HydrateFallback,errorElement:o.props.errorElement,ErrorBoundary:o.props.ErrorBoundary,hasErrorBoundary:o.props.hasErrorBoundary===!0||o.props.ErrorBoundary!=null||o.props.errorElement!=null,shouldRevalidate:o.props.shouldRevalidate,handle:o.props.handle,lazy:o.props.lazy};o.props.children&&(s.children=me(o.props.children,u)),r.push(s)}),r}var ae="get",oe="application/x-www-form-urlencoded";function le(e){return e!=null&&typeof e.tagName=="string"}function Qt(e){return le(e)&&e.tagName.toLowerCase()==="button"}function Zt(e){return le(e)&&e.tagName.toLowerCase()==="form"}function er(e){return le(e)&&e.tagName.toLowerCase()==="input"}function tr(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function rr(e,t){return e.button===0&&(!t||t==="_self")&&!tr(e)}var ne=null;function nr(){if(ne===null)try{new FormData(document.createElement("form"),0),ne=!1}catch{ne=!0}return ne}var ar=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function he(e){return e!=null&&!ar.has(e)?(I(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${oe}"`),null):e}function or(e,t){let r,o,a,u,s;if(Zt(e)){let c=e.getAttribute("action");o=c?B(c,t):null,r=e.getAttribute("method")||ae,a=he(e.getAttribute("enctype"))||oe,u=new FormData(e)}else if(Qt(e)||er(e)&&(e.type==="submit"||e.type==="image")){let c=e.form;if(c==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||c.getAttribute("action");if(o=l?B(l,t):null,r=e.getAttribute("formmethod")||c.getAttribute("method")||ae,a=he(e.getAttribute("formenctype"))||he(c.getAttribute("enctype"))||oe,u=new FormData(c,e),!nr()){let{name:n,type:d,value:g}=e;if(d==="image"){let y=n?`${n}.`:"";u.append(`${y}x`,"0"),u.append(`${y}y`,"0")}else n&&u.append(n,g)}}else{if(le(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');r=ae,o=null,a=oe,s=e}return u&&a==="text/plain"&&(s=u,u=void 0),{action:o,method:r.toLowerCase(),encType:a,formData:u,body:s}}function Ee(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function ir(e,t){if(e.id in t)return t[e.id];try{let r=await import(e.module);return t[e.id]=r,r}catch(r){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(r),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function ur(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function lr(e,t,r){let o=await Promise.all(e.map(async a=>{let u=t.routes[a.route.id];if(u){let s=await ir(u,r);return s.links?s.links():[]}return[]}));return dr(o.flat(1).filter(ur).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function $e(e,t,r,o,a,u){let s=(l,n)=>r[n]?l.route.id!==r[n].route.id:!0,c=(l,n)=>{var d;return r[n].pathname!==l.pathname||((d=r[n].route.path)==null?void 0:d.endsWith("*"))&&r[n].params["*"]!==l.params["*"]};return u==="assets"?t.filter((l,n)=>s(l,n)||c(l,n)):u==="data"?t.filter((l,n)=>{var g;let d=o.routes[l.route.id];if(!d||!d.hasLoader)return!1;if(s(l,n)||c(l,n))return!0;if(l.route.shouldRevalidate){let y=l.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:((g=r[0])==null?void 0:g.params)||{},nextUrl:new URL(e,window.origin),nextParams:l.params,defaultShouldRevalidate:!0});if(typeof y=="boolean")return y}return!0}):[]}function sr(e,t,{includeHydrateFallback:r}={}){return cr(e.map(o=>{let a=t.routes[o.route.id];if(!a)return[];let u=[a.module];return a.clientActionModule&&(u=u.concat(a.clientActionModule)),a.clientLoaderModule&&(u=u.concat(a.clientLoaderModule)),r&&a.hydrateFallbackModule&&(u=u.concat(a.hydrateFallbackModule)),a.imports&&(u=u.concat(a.imports)),u}).flat(1))}function cr(e){return[...new Set(e)]}function fr(e){let t={},r=Object.keys(e).sort();for(let o of r)t[o]=e[o];return t}function dr(e,t){let r=new Set;return new Set(t),e.reduce((o,a)=>{let u=JSON.stringify(fr(a));return r.has(u)||(r.add(u),o.push({key:u,link:a})),o},[])}function hr(e,t){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":t&&B(r.pathname,t)==="/"?r.pathname=`${t.replace(/\/$/,"")}/_root.data`:r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}function qe(){let e=f.useContext(V);return Ee(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function pr(){let e=f.useContext(ue);return Ee(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Re=f.createContext(void 0);Re.displayName="FrameworkContext";function Ke(){let e=f.useContext(Re);return Ee(e,"You must render this element inside a <HydratedRouter> element"),e}function mr(e,t){let r=f.useContext(Re),[o,a]=f.useState(!1),[u,s]=f.useState(!1),{onFocus:c,onBlur:l,onMouseEnter:n,onMouseLeave:d,onTouchStart:g}=t,y=f.useRef(null);f.useEffect(()=>{if(e==="render"&&s(!0),e==="viewport"){let w=m=>{m.forEach(C=>{s(C.isIntersecting)})},v=new IntersectionObserver(w,{threshold:.5});return y.current&&v.observe(y.current),()=>{v.disconnect()}}},[e]),f.useEffect(()=>{if(o){let w=setTimeout(()=>{s(!0)},100);return()=>{clearTimeout(w)}}},[o]);let E=()=>{a(!0)},p=()=>{a(!1),s(!1)};return r?e!=="intent"?[u,y,{}]:[u,y,{onFocus:X(c,E),onBlur:X(l,p),onMouseEnter:X(n,E),onMouseLeave:X(d,p),onTouchStart:X(g,E)}]:[!1,y,{}]}function X(e,t){return r=>{e&&e(r),r.defaultPrevented||t(r)}}function yr({page:e,...t}){let{router:r}=qe(),o=f.useMemo(()=>Ne(r.routes,e,r.basename),[r.routes,e,r.basename]);return o?f.createElement(vr,{page:e,matches:o,...t}):null}function gr(e){let{manifest:t,routeModules:r}=Ke(),[o,a]=f.useState([]);return f.useEffect(()=>{let u=!1;return lr(e,t,r).then(s=>{u||a(s)}),()=>{u=!0}},[e,t,r]),o}function vr({page:e,matches:t,...r}){let o=q(),{manifest:a,routeModules:u}=Ke(),{basename:s}=qe(),{loaderData:c,matches:l}=pr(),n=f.useMemo(()=>$e(e,t,l,a,o,"data"),[e,t,l,a,o]),d=f.useMemo(()=>$e(e,t,l,a,o,"assets"),[e,t,l,a,o]),g=f.useMemo(()=>{if(e===o.pathname+o.search+o.hash)return[];let p=new Set,w=!1;if(t.forEach(m=>{var R;let C=a.routes[m.route.id];!C||!C.hasLoader||(!n.some(O=>O.route.id===m.route.id)&&m.route.id in c&&((R=u[m.route.id])!=null&&R.shouldRevalidate)||C.hasClientLoader?w=!0:p.add(m.route.id))}),p.size===0)return[];let v=hr(e,s);return w&&p.size>0&&v.searchParams.set("_routes",t.filter(m=>p.has(m.route.id)).map(m=>m.route.id).join(",")),[v.pathname+v.search]},[s,c,o,a,n,t,e,u]),y=f.useMemo(()=>sr(d,a),[d,a]),E=gr(d);return f.createElement(f.Fragment,null,g.map(p=>f.createElement("link",{key:p,rel:"prefetch",as:"fetch",href:p,...r})),y.map(p=>f.createElement("link",{key:p,rel:"modulepreload",href:p,...r})),E.map(({key:p,link:w})=>f.createElement("link",{key:p,...w})))}function Er(...e){return t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})}}var Ve=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Ve&&(window.__reactRouterVersion="7.3.0")}catch{}function Dr({basename:e,children:t,window:r}){let o=f.useRef();o.current==null&&(o.current=lt({window:r,v5Compat:!0}));let a=o.current,[u,s]=f.useState({action:a.action,location:a.location}),c=f.useCallback(l=>{f.startTransition(()=>s(l))},[s]);return f.useLayoutEffect(()=>a.listen(c),[a,c]),f.createElement(Xt,{basename:e,children:t,location:u.location,navigationType:u.action,navigator:a})}var Ge=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Je=f.forwardRef(function({onClick:t,discover:r="render",prefetch:o="none",relative:a,reloadDocument:u,replace:s,state:c,target:l,to:n,preventScrollReset:d,viewTransition:g,...y},E){let{basename:p}=f.useContext(H),w=typeof n=="string"&&Ge.test(n),v,m=!1;if(typeof n=="string"&&w&&(v=n,Ve))try{let A=new URL(window.location.href),M=n.startsWith("//")?new URL(A.protocol+n):new URL(n),G=B(M.pathname,p);M.origin===A.origin&&G!=null?n=G+M.search+M.hash:m=!0}catch{I(!1,`<Link to="${n}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let C=Dt(n,{relative:a}),[R,O,b]=mr(o,y),U=xr(n,{replace:s,state:c,target:l,preventScrollReset:d,relative:a,viewTransition:g});function N(A){t&&t(A),A.defaultPrevented||U(A)}let F=f.createElement("a",{...y,...b,href:v||C,onClick:m||u?t:N,ref:Er(E,O),target:l,"data-discover":!w&&r==="render"?"true":void 0});return R&&!w?f.createElement(f.Fragment,null,F,f.createElement(yr,{page:C})):F});Je.displayName="Link";var Rr=f.forwardRef(function({"aria-current":t="page",caseSensitive:r=!1,className:o="",end:a=!1,style:u,to:s,viewTransition:c,children:l,...n},d){let g=te(s,{relative:n.relative}),y=q(),E=f.useContext(ue),{navigator:p,basename:w}=f.useContext(H),v=E!=null&&Tr(g)&&c===!0,m=p.encodeLocation?p.encodeLocation(g).pathname:g.pathname,C=y.pathname,R=E&&E.navigation&&E.navigation.location?E.navigation.location.pathname:null;r||(C=C.toLowerCase(),R=R?R.toLowerCase():null,m=m.toLowerCase()),R&&w&&(R=B(R,w)||R);const O=m!=="/"&&m.endsWith("/")?m.length-1:m.length;let b=C===m||!a&&C.startsWith(m)&&C.charAt(O)==="/",U=R!=null&&(R===m||!a&&R.startsWith(m)&&R.charAt(m.length)==="/"),N={isActive:b,isPending:U,isTransitioning:v},F=b?t:void 0,A;typeof o=="function"?A=o(N):A=[o,b?"active":null,U?"pending":null,v?"transitioning":null].filter(Boolean).join(" ");let M=typeof u=="function"?u(N):u;return f.createElement(Je,{...n,"aria-current":F,className:A,ref:d,style:M,to:s,viewTransition:c},typeof l=="function"?l(N):l)});Rr.displayName="NavLink";var wr=f.forwardRef(({discover:e="render",fetcherKey:t,navigate:r,reloadDocument:o,replace:a,state:u,method:s=ae,action:c,onSubmit:l,relative:n,preventScrollReset:d,viewTransition:g,...y},E)=>{let p=br(),w=Pr(c,{relative:n}),v=s.toLowerCase()==="get"?"get":"post",m=typeof c=="string"&&Ge.test(c),C=R=>{if(l&&l(R),R.defaultPrevented)return;R.preventDefault();let O=R.nativeEvent.submitter,b=(O==null?void 0:O.getAttribute("formmethod"))||s;p(O||R.currentTarget,{fetcherKey:t,method:b,navigate:r,replace:a,state:u,relative:n,preventScrollReset:d,viewTransition:g})};return f.createElement("form",{ref:E,method:v,action:w,onSubmit:o?l:C,...y,"data-discover":!m&&e==="render"?"true":void 0})});wr.displayName="Form";function Cr(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Xe(e){let t=f.useContext(V);return L(t,Cr(e)),t}function xr(e,{target:t,replace:r,state:o,preventScrollReset:a,relative:u,viewTransition:s}={}){let c=Nt(),l=q(),n=te(e,{relative:u});return f.useCallback(d=>{if(rr(d,t)){d.preventDefault();let g=r!==void 0?r:Q(l)===Q(n);c(e,{replace:g,state:o,preventScrollReset:a,relative:u,viewTransition:s})}},[l,c,n,r,o,t,e,a,u,s])}var Sr=0,_r=()=>`__${String(++Sr)}__`;function br(){let{router:e}=Xe("useSubmit"),{basename:t}=f.useContext(H),r=qt();return f.useCallback(async(o,a={})=>{let{action:u,method:s,encType:c,formData:l,body:n}=or(o,t);if(a.navigate===!1){let d=a.fetcherKey||_r();await e.fetch(d,r,a.action||u,{preventScrollReset:a.preventScrollReset,formData:l,body:n,formMethod:a.method||s,formEncType:a.encType||c,flushSync:a.flushSync})}else await e.navigate(a.action||u,{preventScrollReset:a.preventScrollReset,formData:l,body:n,formMethod:a.method||s,formEncType:a.encType||c,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,r])}function Pr(e,{relative:t}={}){let{basename:r}=f.useContext(H),o=f.useContext(W);L(o,"useFormAction must be used inside a RouteContext");let[a]=o.matches.slice(-1),u={...te(e||".",{relative:t})},s=q();if(e==null){u.search=s.search;let c=new URLSearchParams(u.search),l=c.getAll("index");if(l.some(d=>d==="")){c.delete("index"),l.filter(g=>g).forEach(g=>c.append("index",g));let d=c.toString();u.search=d?`?${d}`:""}}return(!e||e===".")&&a.route.index&&(u.search=u.search?u.search.replace(/^\?/,"?index&"):"?index"),r!=="/"&&(u.pathname=u.pathname==="/"?r:j([r,u.pathname])),Q(u)}function Tr(e,t={}){let r=f.useContext(je);L(r!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=Xe("useViewTransitionState"),a=te(e,{relative:t.relative});if(!r.isTransitioning)return!1;let u=B(r.currentLocation.pathname,o)||r.currentLocation.pathname,s=B(r.nextLocation.pathname,o)||r.nextLocation.pathname;return ie(a.pathname,s)!=null||ie(a.pathname,u)!=null}new TextEncoder;var Or=it();const Nr=Ae(Or);export{Dr as H,at as R,$r as a,Or as b,Nr as c,De as d,kr as e,Lr as f,Ae as g,it as h,Ar as i,Jt as j,f as r,Nt as u};
