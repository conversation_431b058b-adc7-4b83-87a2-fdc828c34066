#!/usr/bin/env python3
"""
Test script to verify the embedding_func fix
"""

import os
import sys
import asyncio
import numpy as np
from lightrag import LightRAG
from lightrag.utils import EmbeddingFunc

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

WORKING_DIR = "./test_rag_storage"

def simple_embedding_func(texts: list[str]) -> np.ndarray:
    """Simple embedding function for testing"""
    # Create random embeddings for testing
    embeddings = []
    for text in texts:
        # Simple hash-based embedding (not for production use)
        embedding = np.random.rand(384).astype(np.float32)
        embeddings.append(embedding)
    return np.array(embeddings)

async def simple_llm_func(prompt: str, system_prompt: str = None, **kwargs) -> str:
    """Simple LLM function for testing"""
    return f"Test response for: {prompt[:50]}..."

async def test_lightrag_initialization():
    """Test LightRAG initialization with proper embedding_func"""
    
    print("Testing LightRAG initialization...")
    
    try:
        # Test 1: Initialize without embedding_func (should fail)
        print("\n1. Testing initialization without embedding_func (should fail)...")
        try:
            rag_no_embed = LightRAG(
                working_dir=WORKING_DIR,
                llm_model_func=simple_llm_func,
                # embedding_func=None  # This should cause an error
            )
            print("❌ ERROR: Should have failed but didn't!")
        except ValueError as e:
            print(f"✅ Expected error caught: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
    
        # Test 2: Initialize with proper embedding_func (should succeed)
        print("\n2. Testing initialization with proper embedding_func...")
        rag = LightRAG(
            working_dir=WORKING_DIR,
            llm_model_func=simple_llm_func,
            embedding_func=EmbeddingFunc(
                embedding_dim=384,
                max_token_size=8192,
                func=simple_embedding_func,
            ),
        )
        print("✅ LightRAG initialized successfully!")
        
        # Test 3: Initialize storages
        print("\n3. Testing storage initialization...")
        await rag.initialize_storages()
        print("✅ Storages initialized successfully!")
        
        # Test 4: Test embedding function
        print("\n4. Testing embedding function...")
        test_texts = ["Hello world", "This is a test"]
        embeddings = await rag.embedding_func(test_texts)
        print(f"✅ Embedding function works! Shape: {embeddings.shape}")
        
        # Cleanup
        await rag.finalize_storages()
        print("✅ All tests passed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Create working directory if it doesn't exist
    if not os.path.exists(WORKING_DIR):
        os.makedirs(WORKING_DIR)
    
    # Run the test
    asyncio.run(test_lightrag_initialization())
    
    # Cleanup test directory
    import shutil
    if os.path.exists(WORKING_DIR):
        shutil.rmtree(WORKING_DIR)
        print(f"Cleaned up test directory: {WORKING_DIR}")
