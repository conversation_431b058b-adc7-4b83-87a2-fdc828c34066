{"settings": {"language": "اللغة", "theme": "السمة", "light": "فاتح", "dark": "دا<PERSON>ن", "system": "النظام"}, "header": {"documents": "المستندات", "knowledgeGraph": "شبكة المعرفة", "retrieval": "الاسترجاع", "api": "واجهة برمجة التطبيقات", "projectRepository": "مستودع المشروع", "logout": "تسجيل الخروج", "themeToggle": {"switchToLight": "التحويل إلى السمة الفاتحة", "switchToDark": "التحويل إلى السمة الداكنة"}}, "login": {"description": "الرجاء إدخال حسابك وكلمة المرور لتسجيل الدخول إلى النظام", "username": "اسم المستخدم", "usernamePlaceholder": "الرجاء إدخال اسم المستخدم", "password": "كلمة المرور", "passwordPlaceholder": "الرجاء إدخال كلمة المرور", "loginButton": "تسجيل الدخول", "loggingIn": "جاري تسجيل الدخول...", "successMessage": "تم تسجيل الدخول بنجاح", "errorEmptyFields": "الرجاء إدخال اسم المستخدم وكلمة المرور", "errorInvalidCredentials": "فشل تسجيل الدخول، يرجى التحقق من اسم المستخدم وكلمة المرور", "authDisabled": "تم تعطيل المصادقة. استخدام وضع بدون تسجيل دخول.", "guestMode": "وضع بدون تسجيل دخول"}, "common": {"cancel": "إلغاء", "save": "<PERSON><PERSON><PERSON>", "saving": "جارٍ الحفظ...", "saveFailed": "فشل الحفظ"}, "documentPanel": {"clearDocuments": {"button": "م<PERSON><PERSON>", "tooltip": "م<PERSON><PERSON> المستندات", "title": "م<PERSON><PERSON> المستندات", "description": "سيؤدي هذا إلى إزالة جميع المستندات من النظام", "warning": "تحذير: سيؤدي هذا الإجراء إلى حذف جميع المستندات بشكل دائم ولا يمكن التراجع عنه!", "confirm": "هل تريد حقًا مسح جميع المستندات؟", "confirmPrompt": "اكتب 'yes' لتأكيد هذا الإجراء", "confirmPlaceholder": "اكتب yes للتأكيد", "clearCache": "مسح كاش نموذج اللغة", "confirmButton": "نعم", "success": "تم مسح المستندات بنجاح", "cacheCleared": "تم مسح ذاكرة التخزين المؤقت بنجاح", "cacheClearFailed": "فشل مسح ذاكرة التخزين المؤقت:\n{{error}}", "failed": "فشل مسح المستندات:\n{{message}}", "error": "فشل مسح المستندات:\n{{error}}"}, "deleteDocuments": {"button": "<PERSON><PERSON><PERSON>", "tooltip": "حذ<PERSON> المستندات المحددة", "title": "حذ<PERSON> المستندات", "description": "سيؤدي هذا إلى حذف المستندات المحددة نهائيًا من النظام", "warning": "تحذير: سيؤدي هذا الإجراء إلى حذف المستندات المحددة نهائيًا ولا يمكن التراجع عنه!", "confirm": "هل تريد حقًا حذف {{count}} مستند(ات) محدد(ة)؟", "confirmPrompt": "اكتب 'yes' لتأكيد هذا الإجراء", "confirmPlaceholder": "اكتب yes للتأكيد", "confirmButton": "نعم", "deleteFileOption": "حذف الملفات المرفوعة أيضًا", "deleteFileTooltip": "حدد هذا الخيار لحذف الملفات المرفوعة المقابلة على الخادم أيضًا", "success": "تم بدء تشغيل خط معالجة حذف المستندات بنجاح", "failed": "فشل حذف المستندات:\n{{message}}", "error": "فشل حذف المستندات:\n{{error}}", "busy": "خط المعالجة مشغول، يرجى المحاولة مرة أخرى لاحقًا", "notAllowed": "لا توجد صلاحية لتنفيذ هذه العملية", "cannotDeleteAll": "لا يمكن حذف جميع المستندات. إذا كنت بحاجة لحذف جميع المستندات، يرجى استخدام ميزة مسح المستندات."}, "deselectDocuments": {"button": "إلغاء التحديد", "tooltip": "إلغاء تحديد جميع المستندات المحددة", "title": "إلغاء تحديد المستندات", "description": "سيؤدي هذا إلى مسح جميع المستندات المحددة ({{count}} محدد)", "confirmButton": "إلغاء تحديد الكل"}, "uploadDocuments": {"button": "رفع", "tooltip": "رفع المستندات", "title": "رفع المستندات", "description": "اسحب وأفلت مستنداتك هنا أو انقر للتصفح.", "single": {"uploading": "جارٍ الرفع {{name}}: {{percent}}%", "success": "نجاح الرفع:\nتم رفع {{name}} بنجاح", "failed": "فشل الرفع:\n{{name}}\n{{message}}", "error": "فشل الرفع:\n{{name}}\n{{error}}"}, "batch": {"uploading": "جارٍ رفع الملفات...", "success": "تم رفع الملفات بنجاح", "error": "فشل رفع بعض الملفات"}, "generalError": "فشل الرفع\n{{error}}", "fileTypes": "الأنواع المدعومة: TXT، MD، DOCX، PDF، PPTX، RTF، ODT، EPUB، HTML، HTM، TEX، JSON، XML، YAML، YML، CSV، LOG، CONF، INI، PROPERTIES، SQL، BAT، SH، C، CPP، PY، JAVA، JS، TS، SWIFT، GO، RB، PHP، CSS، SCSS، LESS", "fileUploader": {"singleFileLimit": "لا يمكن رفع أكثر من ملف واحد في المرة الواحدة", "maxFilesLimit": "لا يمكن رفع أكثر من {{count}} ملفات", "fileRejected": "تم رفض الملف {{name}}", "unsupportedType": "نوع الملف غير مدعوم", "fileTooLarge": "حجم الملف كبير جدًا، الحد الأقصى {{maxSize}}", "dropHere": "أفلت الملفات هنا", "dragAndDrop": "اسحب وأفلت الملفات هنا، أو انقر للاختيار", "removeFile": "إزالة الملف", "uploadDescription": "يمكنك رفع {{isMultiple ? 'عدة' : count}} ملفات (حتى {{maxSize}} لكل منها)", "duplicateFile": "اسم الملف موجود بالفعل في ذاكرة التخزين المؤقت للخادم"}}, "documentManager": {"title": "إدارة المستندات", "scanButton": "م<PERSON><PERSON>", "scanTooltip": "مسح المستندات ضوئيًا في مجلد الإدخال", "pipelineStatusButton": "حالة خط المعالجة", "pipelineStatusTooltip": "عرض حالة خط المعالجة", "uploadedTitle": "المستندات المرفوعة", "uploadedDescription": "قائمة المستندات المرفوعة وحالاتها.", "emptyTitle": "لا توجد مستندات", "emptyDescription": "لا توجد مستندات مرفوعة بعد.", "columns": {"id": "المعرف", "summary": "الملخص", "status": "الحالة", "length": "الطول", "chunks": "الأجزاء", "created": "تم الإنشاء", "updated": "تم التحديث", "metadata": "البيانات الوصفية", "select": "اختيار"}, "status": {"all": "الكل", "completed": "مكتمل", "processing": "قيد المعالجة", "pending": "معلق", "failed": "فشل"}, "errors": {"loadFailed": "فشل تحميل المستندات\n{{error}}", "scanFailed": "فشل مسح المستندات\n{{error}}", "scanProgressFailed": "فشل الحصول على تقدم المسح\n{{error}}"}, "fileNameLabel": "اسم الملف", "showButton": "<PERSON><PERSON><PERSON>", "hideButton": "إخفاء", "showFileNameTooltip": "عرض اسم الملف", "hideFileNameTooltip": "إخفاء اسم الملف"}, "pipelineStatus": {"title": "حالة خط المعالجة", "busy": "<PERSON>ط المعالجة مشغول", "requestPending": "الطلب معلق", "jobName": "اسم المهمة", "startTime": "وقت البدء", "progress": "التقدم", "unit": "دفعة", "latestMessage": "آخر رسالة", "historyMessages": "سجل الرسائل", "errors": {"fetchFailed": "فشل في جلب حالة خط المعالجة\n{{error}}"}}}, "graphPanel": {"dataIsTruncated": "تم اقتصار بيانات الرسم البياني على الحد الأقصى للعقد", "statusDialog": {"title": "إعدادات خادم LightRAG", "description": "عرض حالة النظام الحالية ومعلومات الاتصال"}, "legend": "المفتاح", "nodeTypes": {"person": "شخص", "category": "فئة", "geo": "كيان جغرافي", "location": "موقع", "organization": "منظمة", "event": "<PERSON><PERSON><PERSON>", "equipment": "معدات", "weapon": "سلاح", "animal": "حيوان", "unknown": "غير معروف", "object": "مصنوع", "group": "مجموعة", "technology": "العلوم"}, "sideBar": {"settings": {"settings": "الإعدادات", "healthCheck": "فحص الحالة", "showPropertyPanel": "إظهار لوحة الخصائص", "showSearchBar": "إظهار شريط البحث", "showNodeLabel": "إظهار تسمية العقدة", "nodeDraggable": "العقدة قابلة للسحب", "showEdgeLabel": "إظهار تسمية الحافة", "hideUnselectedEdges": "إخفاء الحواف غير المحددة", "edgeEvents": "أحداث الحافة", "maxQueryDepth": "أق<PERSON>ى عمق للاستعلام", "maxNodes": "الح<PERSON> الأقصى للعقد", "maxLayoutIterations": "أقصى تكرارات التخطيط", "resetToDefault": "إعادة التعيين إلى الافتراضي", "edgeSizeRange": "نطاق حجم الحافة", "depth": "D", "max": "Max", "degree": "الدرجة", "apiKey": "مفتاح واجهة برمجة التطبيقات", "enterYourAPIkey": "أدخل مفتاح واجهة برمجة التطبيقات الخاص بك", "save": "<PERSON><PERSON><PERSON>", "refreshLayout": "تحديث التخطيط"}, "zoomControl": {"zoomIn": "تكبير", "zoomOut": "تصغير", "resetZoom": "إعادة تعيين التكبير", "rotateCamera": "تدوير في اتجاه عقارب الساعة", "rotateCameraCounterClockwise": "تدوير عكس اتجاه عقارب الساعة"}, "layoutsControl": {"startAnimation": "بدء حركة التخطيط", "stopAnimation": "إيقا<PERSON> حركة التخطيط", "layoutGraph": "تخطيط الرسم البياني", "layouts": {"Circular": "دائري", "Circlepack": "حزمة دائرية", "Random": "عشوائي", "Noverlaps": "بدون تداخل", "Force Directed": "موجه بالقوة", "Force Atlas": "أطلس القوة"}}, "fullScreenControl": {"fullScreen": "شاشة كاملة", "windowed": "نوافذ"}, "legendControl": {"toggleLegend": "تبديل المفتاح"}}, "statusIndicator": {"connected": "متصل", "disconnected": "<PERSON>ير متصل"}, "statusCard": {"unavailable": "معلومات الحالة غير متوفرة", "storageInfo": "معلومات التخزين", "workingDirectory": "دليل العمل", "inputDirectory": "دليل الإدخال", "llmConfig": "تكوين نموذج اللغة الكبير", "llmBinding": "ربط نموذج اللغة الكبير", "llmBindingHost": "مضيف ربط نموذج اللغة الكبير", "llmModel": "نموذج اللغة الكبير", "maxTokens": "<PERSON><PERSON><PERSON><PERSON> عدد من الرموز", "embeddingConfig": "تكوين التضمين", "embeddingBinding": "ربط التضمين", "embeddingBindingHost": "مضيف ربط التضمين", "embeddingModel": "نموذج التضمين", "storageConfig": "تكوين التخزين", "kvStorage": "تخزين المفتاح-القيمة", "docStatusStorage": "تخزين حالة المستند", "graphStorage": "تخزين الرسم البياني", "vectorStorage": "تخزين المتجهات"}, "propertiesView": {"editProperty": "تعديل {{property}}", "editPropertyDescription": "قم بتحرير قيمة الخاصية في منطقة النص أدناه.", "errors": {"duplicateName": "اسم العقدة موجود بالفعل", "updateFailed": "فشل تحديث العقدة", "tryAgainLater": "يرجى المحاولة مرة أخرى لاحقًا"}, "success": {"entityUpdated": "تم تحديث العقدة بنجاح", "relationUpdated": "تم تحديث العلاقة بنجاح"}, "node": {"title": "عقدة", "id": "المعرف", "labels": "التسميات", "degree": "الدرجة", "properties": "الخصائص", "relationships": "العلاقات (دا<PERSON>ل الرسم الفرعي)", "expandNode": "توسيع العقدة", "pruneNode": "تقليم العقدة", "deleteAllNodesError": "رفض حذ<PERSON> ج<PERSON>يع العقد في الرسم البياني", "nodesRemoved": "تم إزالة {{count}} عقدة، بما في ذلك العقد اليتيمة", "noNewNodes": "لم يتم العثور على عقد قابلة للتوسيع", "propertyNames": {"description": "الوصف", "entity_id": "الاسم", "entity_type": "النوع", "source_id": "معر<PERSON> المص<PERSON>ر", "Neighbour": "الجار", "file_path": "المصدر", "keywords": "الكلمات الرئيسية", "weight": "الوزن"}}, "edge": {"title": "علاقة", "id": "المعرف", "type": "النوع", "source": "المصدر", "target": "الهدف", "properties": "الخصائص"}}, "search": {"placeholder": "ابحث في العقد...", "message": "و {{count}} آخرون"}, "graphLabels": {"selectTooltip": "حدد تسمية الاستعلام", "noLabels": "لم يتم العثور على تسميات", "label": "التسمية", "placeholder": "ابحث في التسميات...", "andOthers": "و {{count}} آخرون", "refreshTooltip": "إعادة تحميل البيانات (بعد إضافة الملف)"}, "emptyGraph": "فارغ (حاول إعادة التحميل)"}, "retrievePanel": {"chatMessage": {"copyTooltip": "نسخ إلى الحافظة", "copyError": "فشل نسخ النص إلى الحافظة"}, "retrieval": {"startPrompt": "ابدأ الاسترجاع بكتابة استفسارك أدناه", "clear": "م<PERSON><PERSON>", "send": "إرسال", "placeholder": "اكتب استفسارك (بادئة وضع الاستعلام: /<Query Mode>)", "error": "خطأ: فشل الحصول على الرد", "queryModeError": "يُسمح فقط بأنماط الاستعلام التالية: {{modes}}", "queryModePrefixInvalid": "بادئة وضع الاستعلام غير صالحة. استخدم: /<الوضع> [مسافة] استفسارك"}, "querySettings": {"parametersTitle": "المعلمات", "parametersDescription": "تكوين معلمات الاستعلام الخاص بك", "queryMode": "وضع الاستعلام", "queryModeTooltip": "حدد استراتيجية الاسترجاع:\n• ساذج: بحث أساسي بدون تقنيات متقدمة\n• محلي: استرجاع معلومات يعتمد على السياق\n• عالمي: يستخدم قاعدة المعرفة العالمية\n• مختلط: يجمع بين الاسترجاع المحلي والعالمي\n• مزيج: يدمج شبكة المعرفة مع الاسترجاع المتجهي\n• تجاوز: يمرر الاستعلام مباشرة إلى LLM بدون استرجاع", "queryModeOptions": {"naive": "ساذج", "local": "م<PERSON><PERSON>ي", "global": "عالمي", "hybrid": "مختلط", "mix": "مزيج", "bypass": "تجاوز"}, "responseFormat": "تنسيق الرد", "responseFormatTooltip": "يحدد تنسيق الرد. أمثلة:\n• فقرات متعددة\n• فقرة واحدة\n• نقاط نقطية", "responseFormatOptions": {"multipleParagraphs": "فقرات متعددة", "singleParagraph": "فقرة واحدة", "bulletPoints": "نقاط نقطية"}, "topK": "أعلى K نتائج", "topKTooltip": "عدد العناصر العلوية للاسترجاع. يمثل الكيانات في وضع 'محلي' والعلاقات في وضع 'عالمي'", "topKPlaceholder": "<PERSON><PERSON><PERSON> النتائج", "maxTokensTextUnit": "أ<PERSON><PERSON>ى عدد من الرموز لوحدة النص", "maxTokensTextUnitTooltip": "الح<PERSON> الأق<PERSON>ى لعدد الرموز المسموح به لكل جزء نصي مسترجع", "maxTokensGlobalContext": "<PERSON><PERSON><PERSON><PERSON> عدد من الرموز للسياق العالمي", "maxTokensGlobalContextTooltip": "الح<PERSON> الأق<PERSON><PERSON> لعدد الرموز المخصص لأوصاف العلاقات في الاسترجاع العالمي", "maxTokensLocalContext": "أ<PERSON><PERSON><PERSON> عدد من الرموز للسياق المحلي", "maxTokensLocalContextTooltip": "الح<PERSON> الأق<PERSON><PERSON> لعدد الرموز المخصص لأوصاف الكيانات في الاسترجاع المحلي", "historyTurns": "دورات التاريخ", "historyTurnsTooltip": "عدد الدورات الكاملة للمحادثة (أزواج المستخدم-المساعد) التي يجب مراعاتها في سياق الرد", "historyTurnsPlaceholder": "عدد دورات التاريخ", "onlyNeedContext": "تحتاج فقط إلى السياق", "onlyNeedContextTooltip": "إذا كان صحيحًا، يتم إرجاع السياق المسترجع فقط دون إنشاء رد", "onlyNeedPrompt": "تحتاج فقط إلى المطالبة", "onlyNeedPromptTooltip": "إذا كان صحيحًا، يتم إرجاع المطالبة المولدة فقط دون إنتاج رد", "streamResponse": "تد<PERSON><PERSON> الرد", "streamResponseTooltip": "إذا كان صحيحًا، يتيح إخراج التدفق للردود في الوقت الفعلي", "userPrompt": "مطالبة مخصصة", "userPromptTooltip": "تقديم متطلبات استجابة إضافية إلى نموذج اللغة الكبير (غير متعلقة بمحتوى الاستعلام، فقط لمعالجة المخرجات).", "userPromptPlaceholder": "أدخل مطالبة مخصصة (اختياري)"}}, "apiSite": {"loading": "جارٍ تحميل وثائق واجهة برمجة التطبيقات..."}, "apiKeyAlert": {"title": "مفتاح واجهة برمجة التطبيقات مطلوب", "description": "الرجاء إدخال مفتاح واجهة برمجة التطبيقات للوصول إلى الخدمة", "placeholder": "أدخل مفتاح واجهة برمجة التطبيقات", "save": "<PERSON><PERSON><PERSON>"}}