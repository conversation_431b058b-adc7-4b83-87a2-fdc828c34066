repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        exclude: ^lightrag/api/webui/
      - id: end-of-file-fixer
        exclude: ^lightrag/api/webui/
      - id: requirements-txt-fixer
        exclude: ^lightrag/api/webui/


  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.6.4
    hooks:
      - id: ruff-format
        exclude: ^lightrag/api/webui/
      - id: ruff
        args: [--fix, --ignore=E402]
        exclude: ^lightrag/api/webui/


  - repo: https://github.com/mgedmin/check-manifest
    rev: "0.49"
    hooks:
      - id: check-manifest
        stages: [manual]
        exclude: ^lightrag/api/webui/
